<script setup lang="ts">
// 首页组件
</script>

<template>
  <div class="home-page">
    <div class="welcome-section">
      <h1>欢迎使用小工单管理系统</h1>
      <p>这是一个基于Vue 3 + Ant Design Vue的工单管理系统布局演示</p>
    </div>

    <div class="feature-cards">
      <a-row :gutter="24">
        <a-col :span="8">
          <a-card title="工单管理" class="feature-card">
            <p>管理和跟踪所有工单的状态和进度</p>
            <a-button type="primary" @click="$router.push('/work-order')"> 查看工单 </a-button>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="生产管理" class="feature-card">
            <p>监控生产流程和产品质量</p>
            <a-button>进入管理</a-button>
          </a-card>
        </a-col>
        <a-col :span="8">
          <a-card title="数据统计" class="feature-card">
            <p>查看各种生产和质量统计数据</p>
            <a-button>查看统计</a-button>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<style scoped lang="scss">
.home-page {
  padding: 0;
}

.welcome-section {
  text-align: center;
  padding: 48px 0;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  margin-bottom: 32px;
}

.welcome-section h1 {
  font-size: 32px;
  margin-bottom: 16px;
  color: white;
}

.welcome-section p {
  font-size: 16px;
  opacity: 0.9;
}

.feature-cards {
  margin-top: 32px;
}

.feature-card {
  text-align: center;
  height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.feature-card :deep(.ant-card-body) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  height: 100%;
}

.feature-card p {
  margin-bottom: 24px;
  color: #666;
}
</style>
