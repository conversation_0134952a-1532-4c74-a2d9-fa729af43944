import type { TableColumn } from '@/common/types'

export const columns: TableColumn[] = [
  {
    title: '库存数量',
    dataIndex: 'stockCount',
    key: 'stockCount',
    visible: true,
    fixed: 'left',
    width: 80,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    visible: true,
    fixed: 'left',
    width: 60,
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    visible: true,
    fixed: 'left',
    width: 120,
  },
  {
    title: '产品编号',
    dataIndex: 'productCode',
    key: 'productCode',
    visible: true,
    width: 120,
  },
  {
    title: '工艺路线',
    dataIndex: 'processRoute',
    key: 'processRoute',
    visible: true,
    width: 120,
  },
  {
    title: '默认供应商',
    dataIndex: 'defaultSupplier',
    key: 'defaultSupplier',
    visible: true,
    width: 120,
  },
  {
    title: '产品规格',
    dataIndex: 'productSpec',
    key: 'productSpec',
    visible: true,
    width: 120,
  },
  {
    title: '子件项数',
    dataIndex: 'subItemCount',
    key: 'subItemCount',
    visible: true,
    width: 120,
  },
]
