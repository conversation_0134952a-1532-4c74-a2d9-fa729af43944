<template>
  <a-modal
    width="900px"
    centered
    wrapClassName="m-modal"
    title="选择产品"
    ok-text="确定"
    cancel-text="取消"
    :open="props.open"
    :body-style="{
      minHeight: '320px',
      maxHeight: 'min(-114px + 94vh, 960px)',
      overflowY: 'auto',
      padding: '12px 24px',
    }"
    @ok="handleOk()"
    @cancel="handleCancel()"
  >
    <ConfigurableTable
      :scroll="{ x: 1000, y: 400 }"
      :row-selection="{ type: 'radio', selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
      :columns="columns"
      :data-source="mockData"
    >
      <template #toolbar-left>
        <a-button type="primary" :icon="h(PlusCircleFilled)" @click="addProductModalOpen = true">
          新建产品
        </a-button>
        <a-button type="text" :icon="h(SortAscendingOutlined)">排序</a-button>
        <a-button type="text" :icon="h(FilterOutlined)">筛选</a-button>
      </template>
      <template #toolbar-right>
        <a-input-search
          v-model:value="searchValue"
          style="min-width: 260px"
          placeholder="可搜索产品名称/编码/规格"
          enter-button
        />
      </template>
    </ConfigurableTable>
  </a-modal>
  <add-product-modal
    :open="addProductModalOpen"
    @cancel="addProductModalOpen = false"
    @confirm="handleAddProductConfirm()"
  />
</template>

<script lang="ts" setup>
import { ref, h } from 'vue'
import { PlusCircleFilled, SortAscendingOutlined, FilterOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'

import { columns } from './columns'
import { mockData } from './mockData'
import ConfigurableTable from '@/components/ConfigurableTable/index.vue'
import addProductModal from '../add-product-modal/add-product-modal.vue'

const props = withDefaults(
  defineProps<{
    open: boolean
  }>(),
  {
    open: false,
  },
)
const emit = defineEmits(['confirm', 'cancel'])

const addProductModalOpen = ref<boolean>(false)
const searchValue = ref<string>('')
const selectedRowKeys = ref<string[]>([])

const onSelectChange = (item: string[]) => {
  selectedRowKeys.value = item
}

const handleAddProductConfirm = () => {
  addProductModalOpen.value = false
}

const handleOk = () => {
  if (selectedRowKeys.value.length === 0) {
    message.error('请选择产品')
    return
  }
  emit('confirm', selectedRowKeys.value)
}

const handleCancel = () => {
  emit('cancel')
}
</script>

<style lang="scss" scoped></style>
