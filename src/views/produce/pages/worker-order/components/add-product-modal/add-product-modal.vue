<template>
  <a-modal
    width="528px"
    centered
    wrapClassName="m-modal"
    title="添加产品"
    ok-text="确定"
    cancel-text="取消"
    :open="props.open"
    :body-style="{
      minHeight: '320px',
      maxHeight: 'min(-114px + 94vh, 960px)',
      overflowY: 'auto',
      padding: '12px 24px',
    }"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      labelAlign="left"
      :label-col="{ style: { width: '108px' } }"
    >
      <a-form-item label="产品编号" name="productCode">
        <a-input v-model:value="formData.productCode" placeholder="请输入，忽略将自动生成" />
      </a-form-item>

      <a-form-item label="产品名称" name="productName" required>
        <a-input v-model:value="formData.productName" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="库存单位" name="stockUnit" required>
        <a-select v-model:value="formData.stockUnit" placeholder="请选择">
          <a-select-option value="个">个</a-select-option>
          <a-select-option value="件">件</a-select-option>
          <a-select-option value="套">套</a-select-option>
          <a-select-option value="箱">箱</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="产品规格" name="productSpec">
        <a-input v-model:value="formData.productSpec" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="产品属性" name="productAttribute" required>
        <a-select v-model:value="formData.productAttribute" placeholder="请选择">
          <a-select-option value="自制">自制</a-select-option>
          <a-select-option value="外购">外购</a-select-option>
          <a-select-option value="委外">委外</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="工艺路线" name="processRoute">
        <a-select v-model:value="formData.processRoute" placeholder="请选择">
          <a-select-option value="标准工艺">标准工艺</a-select-option>
          <a-select-option value="简化工艺">简化工艺</a-select-option>
          <a-select-option value="特殊工艺">特殊工艺</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="默认供应商" name="defaultSupplier">
        <a-select v-model:value="formData.defaultSupplier" placeholder="请选择">
          <a-select-option value="供应商A">供应商A</a-select-option>
          <a-select-option value="供应商B">供应商B</a-select-option>
          <a-select-option value="供应商C">供应商C</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="最大库存" name="maxStock">
        <a-input-number
          v-model:value="formData.maxStock"
          placeholder="请输入"
          style="width: 100%"
          :min="0"
        />
      </a-form-item>

      <a-form-item label="最小库存" name="minStock">
        <a-input-number
          v-model:value="formData.minStock"
          placeholder="请输入"
          style="width: 100%"
          :min="0"
        />
      </a-form-item>

      <a-form-item label="安全库存" name="safeStock">
        <a-input-number
          v-model:value="formData.safeStock"
          placeholder="请输入"
          style="width: 100%"
          :min="0"
        />
      </a-form-item>

      <a-form-item label="库存数量" name="stockQuantity">
        <a-input-number
          disabled
          v-model:value="formData.stockQuantity"
          placeholder="请输入"
          style="width: 100%"
          :min="0"
        />
      </a-form-item>

      <a-form-item label="成本单价" name="costPrice">
        <a-input-number
          v-model:value="formData.costPrice"
          placeholder="请输入"
          style="width: 100%"
          :min="0"
        />
      </a-form-item>

      <a-form-item label="销售单价" name="salePrice">
        <a-input-number
          v-model:value="formData.salePrice"
          placeholder="请输入"
          style="width: 100%"
          :min="0"
        />
      </a-form-item>

      <a-form-item label="关键部件" name="isKeyComponent">
        <a-select v-model:value="formData.isKeyComponent" placeholder="请选择">
          <a-select-option value="是">是</a-select-option>
          <a-select-option value="否">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="制造模式" name="manufacturingMode">
        <a-select v-model:value="formData.manufacturingMode" placeholder="请选择">
          <a-select-option value="按单生产">按单生产</a-select-option>
          <a-select-option value="按库存生产">按库存生产</a-select-option>
          <a-select-option value="混合模式">混合模式</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import type { FormInstance } from 'ant-design-vue'
import { message } from 'ant-design-vue'

const props = withDefaults(
  defineProps<{
    open: boolean
  }>(),
  {
    open: false,
  },
)

const emit = defineEmits(['confirm', 'cancel'])

const formRef = ref<FormInstance>()

// 表单数据
const formData = ref({
  productCode: '',
  productName: '',
  stockUnit: undefined,
  productSpec: '',
  productAttribute: '自制',
  processRoute: undefined,
  defaultSupplier: undefined,
  maxStock: undefined,
  minStock: undefined,
  safeStock: undefined,
  stockQuantity: undefined,
  costPrice: undefined,
  salePrice: undefined,
  isKeyComponent: '否',
  manufacturingMode: undefined,
})

// 表单验证规则
const rules = {
  productName: [{ required: true, message: '请输入产品名称', trigger: 'blur' }],
  stockUnit: [{ required: true, message: '请选择库存单位', trigger: 'change' }],
  productAttribute: [{ required: true, message: '请选择产品属性', trigger: 'change' }],
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    productCode: '',
    productName: '',
    stockUnit: undefined,
    productSpec: '',
    productAttribute: '自制',
    processRoute: undefined,
    defaultSupplier: undefined,
    maxStock: undefined,
    minStock: undefined,
    safeStock: undefined,
    stockQuantity: undefined,
    costPrice: undefined,
    salePrice: undefined,
    isKeyComponent: '否',
    manufacturingMode: undefined,
  })
  formRef.value?.resetFields()
}

// 确认按钮点击事件
const handleOk = async () => {
  try {
    await formRef.value?.validate()
    const formData = formRef.value?.getFieldsValue()

    // TODO: 调用接口
    message.success('产品添加成功！')
    emit('confirm', formData)

    resetForm()
  } catch (error) {
    console.log('表单验证失败:', error)
    message.error('请检查表单填写是否正确')
  }
}

// 取消按钮点击事件
const handleCancel = () => {
  emit('cancel', false)
  resetForm()
}

// 监听弹窗关闭，重置表单
watch(
  () => props.open,
  (newVal: boolean) => {
    if (!newVal) {
      resetForm()
    }
  },
)
</script>

<style lang="scss" scoped>
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

:deep(.ant-input-number) {
  width: 100%;
}
</style>
