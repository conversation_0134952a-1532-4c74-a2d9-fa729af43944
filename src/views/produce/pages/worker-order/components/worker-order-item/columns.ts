import type { TableColumn } from '@/common/types'

export const taskColumns: TableColumn[] = [
  {
    title: '工序名称',
    dataIndex: 'taskNo',
    key: 'taskNo',
    width: 120,
    visible: true,
  },
  {
    title: '工序编号',
    dataIndex: 'productNo',
    key: 'productNo',
    width: 120,
    visible: true,
  },
  {
    title: '报工权限',
    dataIndex: 'productSpec',
    key: 'productSpec',
    width: 120,
    visible: true,
  },
  {
    title: '分配列表',
    dataIndex: 'specList',
    key: 'specList',
    width: 120,
    visible: true,
  },
  {
    title: '报工数配比',
    dataIndex: 'workCount',
    key: 'workCount',
    width: 120,
    visible: true,
  },
  {
    title: '计划数',
    dataIndex: 'planCount',
    key: 'planCount',
    visible: true,
  },
  {
    title: '良品数',
    dataIndex: 'goodCount',
    key: 'goodCount',
    visible: true,
  },
  {
    title: '不良品数',
    dataIndex: 'badCount',
    key: 'badCount',
    visible: true,
  },
  {
    title: '不良品项列表',
    dataIndex: 'badList',
    key: 'badList',
    visible: true,
  },
  {
    title: '计划开始时间',
    dataIndex: 'planStartTime',
    key: 'planStartTime',
    visible: true,
  },
  {
    title: '计划结束时间',
    dataIndex: 'planEndTime',
    key: 'planEndTime',
    visible: true,
  },
  {
    title: '实际开始时间',
    dataIndex: 'actualStartTime',
    key: 'actualStartTime',
    visible: true,
  },
  {
    title: '实际结束时间',
    dataIndex: 'actualEndTime',
    key: 'actualEndTime',
    visible: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    visible: true,
  },
]

export const materialColumns: TableColumn[] = [
  {
    title: '产品编号',
    dataIndex: 'productNo',
    key: 'productNo',
    visible: true,
  },
  {
    title: '产品名称',
    dataIndex: 'productName',
    key: 'productName',
    visible: true,
  },
  {
    title: '产品规格',
    dataIndex: 'productSpec',
    key: 'productSpec',
    visible: true,
  },
  {
    title: '产品属性',
    dataIndex: 'productAttr',
    key: 'productAttr',
    visible: true,
  },
  {
    title: '库存数量',
    dataIndex: 'stockCount',
    key: 'stockCount',
    visible: true,
  },
  {
    title: '单位',
    dataIndex: 'unit',
    key: 'unit',
    visible: true,
  },
  {
    title: '单位用量',
    dataIndex: 'unitUsage',
    key: 'unitUsage',
    visible: true,
  },
  {
    title: '需求数量',
    dataIndex: 'demandCount',
    key: 'demandCount',
    visible: true,
  },
  {
    title: '未领料数量',
    dataIndex: 'unclaimedCount',
    key: 'unclaimedCount',
    visible: true,
  },
  {
    title: '实际用料数量',
    dataIndex: 'actualUsageCount',
    key: 'actualUsageCount',
    visible: true,
  },
  {
    title: '已领料数量',
    dataIndex: 'claimedCount',
    key: 'claimedCount',
    visible: true,
  },
  {
    title: '已退料数量',
    dataIndex: 'returnedCount',
    key: 'returnedCount',
    visible: true,
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    visible: true,
  },
]
