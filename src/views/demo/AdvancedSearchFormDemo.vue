<template>
  <div class="advanced-search-form-demo">
    <h2>AdvancedSearchForm 组件演示</h2>
    <p>以下是如何在 WorkOrder 页面中使用 AdvancedSearchForm 组件的示例：</p>
    <a-alert
      message="智能检测功能"
      description="组件会自动检测传入的插槽数量，无需手动指定 totalItems 属性！"
      type="info"
      show-icon
      style="margin-bottom: 16px"
    />

    <!-- 原始风格的搜索表单 -->
    <div class="demo-section">
      <h3>WorkOrder 页面风格示例</h3>
      <div class="filter-row">
        <AdvancedSearchForm
          ref="workOrderFormRef"
          :collapsed-items="3"
          @search="handleWorkOrderSearch"
          @expand-change="handleExpandChange"
        >
          <!-- 工单编号 -->
          <template #item-1>
            <a-form-item
              name="workOrderNo"
              label="计划结束时间"
              :rules="[{ required: false, message: '请输入工单编号' }]"
            >
              <a-input placeholder="请输入工单编号" />
            </a-form-item>
          </template>

          <!-- 产品编号 -->
          <template #item-2>
            <a-form-item
              name="productNo"
              label="产品编号"
              :rules="[{ required: false, message: '请输入产品编号' }]"
            >
              <a-input placeholder="请输入产品编号" />
            </a-form-item>
          </template>

          <!-- 产品名称 -->
          <template #item-3>
            <a-form-item
              name="productName"
              label="产品名称"
              :rules="[{ required: false, message: '请选择产品名称' }]"
            >
              <a-select placeholder="请选择产品名称" :options="productOptions" />
            </a-form-item>
          </template>

          <!-- 工单状态 -->
          <template #item-4>
            <a-form-item
              name="status"
              label="工单状态"
              :rules="[{ required: false, message: '请选择工单状态' }]"
            >
              <a-select placeholder="请选择工单状态" :options="statusOptions" />
            </a-form-item>
          </template>

          <!-- 紧急程度 -->
          <template #item-5>
            <a-form-item
              name="urgency"
              label="紧急程度"
              :rules="[{ required: false, message: '请选择紧急程度' }]"
            >
              <a-select placeholder="请选择紧急程度" :options="urgencyOptions" />
            </a-form-item>
          </template>

          <!-- 创建日期范围 -->
          <template #item-6>
            <a-form-item
              name="dateRange"
              label="创建日期"
              :rules="[{ required: false, message: '请选择日期范围' }]"
            >
              <a-range-picker />
            </a-form-item>
          </template>

          <!-- 负责人 -->
          <template #item-7>
            <a-form-item
              name="assignee"
              label="负责人"
              :rules="[{ required: false, message: '请输入负责人' }]"
            >
              <a-input placeholder="请输入负责人姓名" />
            </a-form-item>
          </template>

          <!-- 压铸机 -->
          <template #item-8>
            <a-form-item
              name="machine"
              label="压铸机"
              :rules="[{ required: false, message: '请选择压铸机' }]"
            >
              <a-select placeholder="请选择压铸机" :options="machineOptions" />
            </a-form-item>
          </template>

          <!-- 工单备注 -->
          <template #item-9>
            <a-form-item
              name="notes"
              label="工单备注"
              :rules="[{ required: false, message: '请输入工单备注' }]"
            >
              <a-input placeholder="请输入工单备注关键词" />
            </a-form-item>
          </template>

          <!-- 客户信息 -->
          <template #item-10>
            <a-form-item
              name="customer"
              label="客户信息"
              :rules="[{ required: false, message: '请输入客户信息' }]"
            >
              <a-input placeholder="请输入客户名称或编号" />
            </a-form-item>
          </template>

          <!-- 自定义操作按钮 -->
          <template #actions="{ onSearch }">
            <a-button type="primary" @click="onSearch">
              <SearchOutlined />
              查询
            </a-button>
            <a-button @click="handleReset">
              <RedoOutlined />
              重置
            </a-button>
            <a-button @click="handleExport">
              <DownloadOutlined />
              导出
            </a-button>
            <a-dropdown>
              <template #overlay>
                <a-menu>
                  <a-menu-item @click="handleBatchAction('delete')"> 批量删除 </a-menu-item>
                  <a-menu-item @click="handleBatchAction('update')"> 批量更新状态 </a-menu-item>
                  <a-menu-item @click="handleBatchAction('assign')"> 批量分配 </a-menu-item>
                </a-menu>
              </template>
              <a-button>
                更多操作
                <DownOutlined />
              </a-button>
            </a-dropdown>
          </template>
        </AdvancedSearchForm>
      </div>
    </div>

    <!-- 演示操作区域 -->
    <div class="demo-section">
      <h3>组件操作演示</h3>
      <div class="demo-controls">
        <a-button @click="manualSearch">手动触发搜索</a-button>
        <a-button @click="resetForm">重置表单</a-button>
        <a-button @click="toggleExpand">切换展开状态</a-button>
        <a-button @click="fillTestData">填充测试数据</a-button>
        <a-button @click="getFormData">获取表单数据</a-button>
      </div>
    </div>

    <!-- 搜索结果展示 -->
    <div class="demo-section">
      <h3>搜索结果</h3>
      <a-card>
        <pre>{{ JSON.stringify(lastSearchData, null, 2) }}</pre>
      </a-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { SearchOutlined, RedoOutlined, DownloadOutlined, DownOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import AdvancedSearchForm from '@/components/AdvancedSearchForm/index.vue'
import type { SelectProps } from 'ant-design-vue'

// 表单引用
const workOrderFormRef = ref()

// 搜索结果数据
const lastSearchData = ref({})

// 下拉选项数据
const productOptions: SelectProps['options'] = [
  { label: '铝合金外壳', value: 'aluminum_case' },
  { label: '不锈钢配件', value: 'steel_parts' },
  { label: '塑料组件', value: 'plastic_components' },
  { label: '电子元器件', value: 'electronic_parts' },
]

const statusOptions: SelectProps['options'] = [
  { label: '待开始', value: 'pending' },
  { label: '进行中', value: 'in_progress' },
  { label: '已暂停', value: 'paused' },
  { label: '已完成', value: 'completed' },
  { label: '已取消', value: 'cancelled' },
]

const urgencyOptions: SelectProps['options'] = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
  { label: '紧急', value: 'urgent' },
]

const machineOptions: SelectProps['options'] = [
  { label: '压铸机-001', value: 'machine_001' },
  { label: '压铸机-002', value: 'machine_002' },
  { label: '压铸机-003', value: 'machine_003' },
  { label: '压铸机-004', value: 'machine_004' },
]

// 事件处理函数
const handleWorkOrderSearch = (formData: Record<string, any>) => {
  console.log('工单搜索数据:', formData)
  lastSearchData.value = formData
  message.success('搜索完成')
}

const handleExpandChange = (expanded: boolean) => {
  console.log('展开状态变化:', expanded)
  message.info(`表单${expanded ? '已展开' : '已收起'}`)
}

const handleReset = () => {
  workOrderFormRef.value?.resetFields()
  lastSearchData.value = {}
  message.success('表单已重置')
}

const handleExport = () => {
  console.log('导出工单数据')
  message.success('数据导出中...')
}

const handleBatchAction = (action: string) => {
  console.log('批量操作:', action)
  message.success(`执行批量${action}操作`)
}

// 演示操作方法
const manualSearch = () => {
  workOrderFormRef.value?.handleSearch()
}

const resetForm = () => {
  workOrderFormRef.value?.resetFields()
  lastSearchData.value = {}
}

const toggleExpand = () => {
  workOrderFormRef.value?.toggleExpand()
}

const fillTestData = () => {
  // 由于 FormInstance 的限制，我们直接设置表单的响应式数据
  // 在实际项目中，推荐使用 v-model 绑定表单数据
  const testData = {
    workOrderNo: 'WO202501001',
    productNo: 'P001',
    productName: 'aluminum_case',
    status: 'in_progress',
    urgency: 'high',
    assignee: '张三',
    machine: 'machine_001',
    notes: '紧急订单',
    customer: '客户A',
  }

  console.log('填充测试数据:', testData)
  message.info('测试数据已填充（请查看控制台）')
}

const getFormData = () => {
  const formData = workOrderFormRef.value?.getFieldsValue()
  console.log('当前表单数据:', formData)
  message.info('表单数据已输出到控制台')
}
</script>

<style lang="scss" scoped>
.advanced-search-form-demo {
  padding: 24px;

  h2 {
    margin-bottom: 16px;
    color: #1890ff;
  }

  h3 {
    margin: 24px 0 16px 0;
    color: #333;
    border-bottom: 1px solid #e8e8e8;
    padding-bottom: 8px;
  }

  p {
    margin-bottom: 24px;
    color: #666;
  }
}

.demo-section {
  margin-bottom: 32px;

  .filter-row {
    padding: 16px;
    background-color: #fafafa;
    border-radius: 6px;
    border: 1px solid #e8e8e8;
  }
}

.demo-controls {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

pre {
  background-color: #f6f8fa;
  padding: 16px;
  border-radius: 6px;
  overflow-x: auto;
  max-height: 300px;
}
</style>
