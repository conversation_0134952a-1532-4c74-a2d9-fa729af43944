<template>
  <div class="layout-alignment-demo">
    <div class="page-header">
      <h1>搜索布局对齐优化演示</h1>
      <p>展示不同标签长度下的布局对齐效果</p>
    </div>

    <!-- 标签长度不一致的情况 -->
    <div class="demo-section">
      <h2>标签长度不一致的对齐效果</h2>
      <div class="demo-block">
        <CollapsibleSearchLayout
          :conditions="mixedLengthConditions"
          :default-expanded="true"
          @search="handleSearch"
        >
          <!-- 短标签 -->
          <template #condition-0>
            <a-form-item label="编号" class="search-form-item">
              <a-input v-model:value="form.no" placeholder="请输入编号" />
            </a-form-item>
          </template>

          <!-- 中等长度标签 -->
          <template #condition-1>
            <a-form-item label="产品名称" class="search-form-item">
              <a-input v-model:value="form.productName" placeholder="请输入产品名称" />
            </a-form-item>
          </template>

          <!-- 长标签 -->
          <template #condition-2>
            <a-form-item label="工单创建时间范围" class="search-form-item">
              <a-range-picker v-model:value="form.timeRange" style="width: 100%" />
            </a-form-item>
          </template>

          <!-- 超长标签 -->
          <template #condition-3>
            <a-form-item label="负责人员及部门信息" class="search-form-item">
              <a-select v-model:value="form.assignee" placeholder="请选择负责人">
                <a-select-option value="user1">张三 - 生产部</a-select-option>
                <a-select-option value="user2">李四 - 质检部</a-select-option>
              </a-select>
            </a-form-item>
          </template>

          <!-- 普通标签 -->
          <template #condition-4>
            <a-form-item label="状态" class="search-form-item">
              <a-select v-model:value="form.status" placeholder="请选择状态">
                <a-select-option value="pending">待处理</a-select-option>
                <a-select-option value="processing">处理中</a-select-option>
                <a-select-option value="completed">已完成</a-select-option>
              </a-select>
            </a-form-item>
          </template>

          <!-- 数字输入 -->
          <template #condition-5>
            <a-form-item label="数量" class="search-form-item">
              <a-input-number
                v-model:value="form.quantity"
                placeholder="请输入数量"
                style="width: 100%"
                :min="0"
              />
            </a-form-item>
          </template>
        </CollapsibleSearchLayout>
      </div>
    </div>

    <!-- 每行4个的布局 -->
    <div class="demo-section">
      <h2>每行4个条件的布局效果</h2>
      <div class="demo-block">
        <CollapsibleSearchLayout
          :conditions="fourPerRowConditions"
          :items-per-row="4"
          :default-expanded="true"
          @search="handleSearch"
        >
          <template #condition-0>
            <a-form-item label="工单编号" class="search-form-item">
              <a-input v-model:value="form2.workOrderNo" placeholder="工单编号" />
            </a-form-item>
          </template>

          <template #condition-1>
            <a-form-item label="产品编号" class="search-form-item">
              <a-input v-model:value="form2.productNo" placeholder="产品编号" />
            </a-form-item>
          </template>

          <template #condition-2>
            <a-form-item label="批次号" class="search-form-item">
              <a-input v-model:value="form2.batchNo" placeholder="批次号" />
            </a-form-item>
          </template>

          <template #condition-3>
            <a-form-item label="机台" class="search-form-item">
              <a-select v-model:value="form2.machine" placeholder="选择机台">
                <a-select-option value="m1">机台-01</a-select-option>
                <a-select-option value="m2">机台-02</a-select-option>
              </a-select>
            </a-form-item>
          </template>

          <template #condition-4>
            <a-form-item label="操作员" class="search-form-item">
              <a-input v-model:value="form2.operator" placeholder="操作员" />
            </a-form-item>
          </template>

          <template #condition-5>
            <a-form-item label="班次" class="search-form-item">
              <a-select v-model:value="form2.shift" placeholder="选择班次">
                <a-select-option value="day">白班</a-select-option>
                <a-select-option value="night">夜班</a-select-option>
              </a-select>
            </a-form-item>
          </template>

          <template #condition-6>
            <a-form-item label="质检状态" class="search-form-item">
              <a-select v-model:value="form2.qcStatus" placeholder="质检状态">
                <a-select-option value="pass">合格</a-select-option>
                <a-select-option value="fail">不合格</a-select-option>
              </a-select>
            </a-form-item>
          </template>

          <template #condition-7>
            <a-form-item label="优先级" class="search-form-item">
              <a-select v-model:value="form2.priority" placeholder="优先级">
                <a-select-option value="high">高</a-select-option>
                <a-select-option value="medium">中</a-select-option>
                <a-select-option value="low">低</a-select-option>
              </a-select>
            </a-form-item>
          </template>
        </CollapsibleSearchLayout>
      </div>
    </div>

    <!-- 复杂表单控件的布局 -->
    <div class="demo-section">
      <h2>复杂表单控件的布局效果</h2>
      <div class="demo-block">
        <CollapsibleSearchLayout
          :conditions="complexConditions"
          :default-expanded="true"
          @search="handleSearch"
        >
          <template #condition-0>
            <a-form-item label="时间范围" class="search-form-item">
              <a-range-picker v-model:value="form3.dateRange" show-time style="width: 100%" />
            </a-form-item>
          </template>

          <template #condition-1>
            <a-form-item label="多选状态" class="search-form-item">
              <a-select
                v-model:value="form3.multiStatus"
                mode="multiple"
                placeholder="可选择多个状态"
                style="width: 100%"
              >
                <a-select-option value="draft">草稿</a-select-option>
                <a-select-option value="pending">待审核</a-select-option>
                <a-select-option value="approved">已审核</a-select-option>
                <a-select-option value="rejected">已拒绝</a-select-option>
              </a-select>
            </a-form-item>
          </template>

          <template #condition-2>
            <a-form-item label="数量范围" class="search-form-item">
              <a-input-group compact style="width: 100%">
                <a-input-number
                  v-model:value="form3.minQuantity"
                  placeholder="最小值"
                  style="width: 50%"
                />
                <a-input-number
                  v-model:value="form3.maxQuantity"
                  placeholder="最大值"
                  style="width: 50%"
                />
              </a-input-group>
            </a-form-item>
          </template>

          <template #condition-3>
            <a-form-item label="级联选择" class="search-form-item">
              <a-cascader
                v-model:value="form3.cascader"
                :options="cascaderOptions"
                placeholder="请选择"
                style="width: 100%"
              />
            </a-form-item>
          </template>
        </CollapsibleSearchLayout>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { message } from 'ant-design-vue'
import CollapsibleSearchLayout from '@/components/CollapsibleSearchLayout/index.vue'

// 表单数据
const form = ref({
  no: '',
  productName: '',
  timeRange: null,
  assignee: '',
  status: '',
  quantity: null,
})

const form2 = ref({
  workOrderNo: '',
  productNo: '',
  batchNo: '',
  machine: '',
  operator: '',
  shift: '',
  qcStatus: '',
  priority: '',
})

const form3 = ref({
  dateRange: null,
  multiStatus: [],
  minQuantity: null,
  maxQuantity: null,
  cascader: [],
})

// 搜索条件配置
const mixedLengthConditions = ref([
  { label: '编号' },
  { label: '产品名称' },
  { label: '工单创建时间范围' },
  { label: '负责人员及部门信息' },
  { label: '状态' },
  { label: '数量' },
])

const fourPerRowConditions = ref([
  { label: '工单编号' },
  { label: '产品编号' },
  { label: '批次号' },
  { label: '机台' },
  { label: '操作员' },
  { label: '班次' },
  { label: '质检状态' },
  { label: '优先级' },
])

const complexConditions = ref([
  { label: '时间范围' },
  { label: '多选状态' },
  { label: '数量范围' },
  { label: '级联选择' },
])

// 级联选择器选项
const cascaderOptions = ref([
  {
    value: 'production',
    label: '生产部门',
    children: [
      { value: 'workshop1', label: '车间一' },
      { value: 'workshop2', label: '车间二' },
    ],
  },
  {
    value: 'quality',
    label: '质检部门',
    children: [
      { value: 'incoming', label: '来料检验' },
      { value: 'process', label: '过程检验' },
      { value: 'final', label: '最终检验' },
    ],
  },
])

// 事件处理
const handleSearch = () => {
  console.log('搜索参数:', { form: form.value, form2: form2.value, form3: form3.value })
  message.success('搜索完成')
}
</script>

<style lang="scss" scoped>
.layout-alignment-demo {
  padding: $spacing-lg;

  .page-header {
    margin-bottom: $spacing-xl;

    h1 {
      color: $gray-800;
      font-size: $font-size-2xl;
      margin-bottom: $spacing-sm;
    }

    p {
      color: $gray-600;
      font-size: $font-size-base;
    }
  }

  .demo-section {
    margin-bottom: $spacing-2xl;

    h2 {
      color: $gray-700;
      font-size: $font-size-xl;
      margin-bottom: $spacing-lg;
      padding-bottom: $spacing-sm;
      border-bottom: 2px solid $primary-color;
    }
  }

  .demo-block {
    padding: $spacing-lg;
    background: $white;
    border-radius: $border-radius-lg;
    box-shadow: $shadow-sm;
    border: 1px solid $gray-200;
  }
}
</style>
