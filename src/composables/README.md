# useTableColumns Composable

一个用于处理表格列配置的 Vue 3 Composable 函数，封装了列的显示、隐藏、固定等常用逻辑。

## 功能特性

- 🔍 **可见列过滤**：自动过滤出 `visible: true` 的列
- 📌 **固定列处理**：正确处理 `fixed` 属性（false 转为 undefined）
- 📊 **统计信息**：提供可见列数量、固定列数量等统计
- ⚡ **响应式**：基于 Vue 3 响应式系统，自动更新
- 🎯 **类型安全**：完整的 TypeScript 类型支持

## API

### 参数

| 参数             | 类型                 | 说明               |
| ---------------- | -------------------- | ------------------ |
| `columnSettings` | `Ref<TableColumn[]>` | 列配置的响应式引用 |

### 返回值

| 属性                    | 类型                         | 说明               |
| ----------------------- | ---------------------------- | ------------------ |
| `visibleColumns`        | `ComputedRef<TableColumn[]>` | 处理后的可见列配置 |
| `visibleColumnCount`    | `ComputedRef<number>`        | 可见列的数量       |
| `fixedColumnCount`      | `ComputedRef<number>`        | 左侧固定列的数量   |
| `rightFixedColumnCount` | `ComputedRef<number>`        | 右侧固定列的数量   |
| `hasFixedColumns`       | `ComputedRef<boolean>`       | 是否有左侧固定列   |
| `hasRightFixedColumns`  | `ComputedRef<boolean>`       | 是否有右侧固定列   |

## 基本用法

### 替换前的代码

```vue
<script setup lang="ts">
import { ref, computed } from 'vue'
import type { TableColumn } from '@/common/types'

const columnSettings = ref<TableColumn[]>([...])

// 每个页面都需要重复这段逻辑
const visibleColumns = computed(() =>
  columnSettings.value
    .filter((col) => col.visible)
    .map((col) => ({
      ...col,
      fixed: col.fixed === false ? undefined : col.fixed,
    }))
)
</script>
```

### 替换后的代码

```vue
<script setup lang="ts">
import { ref } from 'vue'
import { useTableColumns } from '@/composables/useTableColumns'
import type { TableColumn } from '@/common/types'

const columnSettings = ref<TableColumn[]>([...])

// 一行代码搞定
const { visibleColumns } = useTableColumns(columnSettings)
</script>
```

## 完整使用示例

```vue
<template>
  <div>
    <!-- 显示统计信息 -->
    <div class="column-stats">
      <span>可见列: {{ visibleColumnCount }}</span>
      <span>固定列: {{ fixedColumnCount }}</span>
      <span v-if="hasRightFixedColumns">右侧固定列: {{ rightFixedColumnCount }}</span>
    </div>

    <!-- 字段配置组件 -->
    <ColumnConfig :columns="columnSettings" @update="handleColumnsUpdate" />

    <!-- 表格 -->
    <a-table
      :columns="visibleColumns"
      :data-source="dataSource"
      :scroll="{ x: hasFixedColumns ? 1200 : undefined }"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useTableColumns } from '@/composables/useTableColumns'
import ColumnConfig from '@/components/ColumnConfig/index.vue'
import type { TableColumn } from '@/common/types'

// 列配置
const columnSettings = ref<TableColumn[]>([
  {
    title: '工单编号',
    dataIndex: 'workOrderNo',
    key: 'workOrderNo',
    visible: true,
    width: 120,
    fixed: 'left',
    order: 1,
  },
  // ... 更多列配置
])

// 使用 composable
const {
  visibleColumns,
  visibleColumnCount,
  fixedColumnCount,
  rightFixedColumnCount,
  hasFixedColumns,
  hasRightFixedColumns,
} = useTableColumns(columnSettings)

// 处理列配置更新
const handleColumnsUpdate = (newColumns: TableColumn[]) => {
  columnSettings.value = newColumns
}

// 表格数据
const dataSource = ref([...])
</script>
```

## 高级用法

### 结合其他逻辑使用

```vue
<script setup lang="ts">
import { ref, computed } from 'vue'
import { useTableColumns } from '@/composables/useTableColumns'

const columnSettings = ref<TableColumn[]>([...])
const { visibleColumns, hasFixedColumns } = useTableColumns(columnSettings)

// 基于 composable 的结果进行进一步处理
const tableScrollConfig = computed(() => ({
  x: hasFixedColumns.value ? 1200 : undefined,
  y: 600,
}))

// 动态计算表格宽度
const tableWidth = computed(() => {
  return visibleColumns.value.reduce((total, col) => {
    return total + (col.width || 100)
  }, 0)
})
</script>
```

## 扩展性

如果需要添加更多列处理逻辑，可以轻松扩展 composable：

```typescript
// 未来可能的扩展
export function useTableColumns(columnSettings: Ref<TableColumn[]>) {
  // 现有逻辑...

  // 新增：按分组过滤列
  const getColumnsByGroup = (group: string) =>
    computed(() => visibleColumns.value.filter((col) => col.group === group))

  // 新增：获取可排序的列
  const sortableColumns = computed(() =>
    visibleColumns.value.filter((col) => col.sortable !== false),
  )

  return {
    visibleColumns,
    // ... 其他现有返回值
    getColumnsByGroup,
    sortableColumns,
  }
}
```

## 注意事项

1. **响应式依赖**：确保传入的 `columnSettings` 是响应式的 `ref` 或 `reactive` 对象
2. **类型安全**：确保 `TableColumn` 类型定义正确导入
3. **性能优化**：composable 内部使用 `computed`，会自动缓存计算结果
4. **向后兼容**：可以与现有代码并存，逐步迁移
