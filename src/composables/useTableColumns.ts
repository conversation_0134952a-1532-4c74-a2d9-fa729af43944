import { computed, type Ref } from 'vue'
import type { TableColumn } from '@/common/types'

/**
 * 表格列管理 Composable
 * 用于处理表格列的显示、隐藏、固定等逻辑
 *
 * @param columnSettings 列配置的响应式引用
 * @returns 处理后的可见列配置
 */
export function useTableColumns(columnSettings: Ref<TableColumn[]>) {
  /**
   * 计算可见的列配置
   * 1. 过滤出 visible 为 true 的列
   * 2. 处理 fixed 属性：false 转为 undefined
   */
  const visibleColumns = computed(() =>
    columnSettings.value
      .filter((col) => col.visible)
      .map((col) => ({
        ...col,
        fixed: col.fixed === false ? undefined : col.fixed,
      })),
  )

  /**
   * 获取可见列的数量
   */
  const visibleColumnCount = computed(
    () => columnSettings.value.filter((col) => col.visible).length,
  )

  /**
   * 获取固定列的数量
   */
  const fixedColumnCount = computed(
    () =>
      columnSettings.value.filter(
        (col) => col.visible && (col.fixed === true || col.fixed === 'left'),
      ).length,
  )

  /**
   * 检查是否有固定列
   */
  const hasFixedColumns = computed(() => fixedColumnCount.value > 0)

  return {
    visibleColumns,
    visibleColumnCount,
    fixedColumnCount,
    hasFixedColumns,
  }
}
