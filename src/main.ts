import { createApp } from 'vue'
import { createPinia } from 'pinia'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/reset.css'

// 将自定义样式放在 Ant Design 样式之后加载，确保覆盖生效
import './assets/styles/index.scss'
// 使用iconfont
import '@/assets/iconfont/iconfont.js'
import SvgIcon from '@/components/SvgIcon/index.vue'

// a-date-picker 使用中文
import dayjs from 'dayjs'
import 'dayjs/locale/zh-cn'
dayjs.locale('zh-cn')

import App from './App.vue'
import router from './router'

const app = createApp(App)

app.use(createPinia())
app.use(router)
app.use(Antd)
app.component('svg-icon', SvgIcon)

app.mount('#app')
