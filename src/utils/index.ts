import dayjs from 'dayjs'
// 获取剩余时间
export function getRemainingTime(endTime: dayjs.Dayjs) {
  const now = dayjs()
  const diffInMinutes = endTime.diff(now, 'minute')

  let timeText = ''

  const absMinutes = Math.abs(diffInMinutes)

  if (absMinutes < 60) {
    timeText = `${absMinutes}分钟`
  } else if (absMinutes < 24 * 60) {
    const hours = Math.floor(absMinutes / 60)
    timeText = `${hours}小时`
  } else {
    const days = Math.floor(absMinutes / (24 * 60))
    const remainingMinutes = absMinutes % (24 * 60)
    const hours = Math.floor(remainingMinutes / 60)
    timeText = hours === 0 ? `${days}天` : `${days}天${hours}小时`
  }

  return diffInMinutes <= 0 ? `已逾期${timeText}` : `剩余${timeText}`
}

// 千分位格式化
export function formatNumber(num: number | string | undefined | null, locale = 'en-US'): string {
  if (num === null || num === undefined || isNaN(Number(num))) return '-'
  return new Intl.NumberFormat(locale).format(Number(num))
}
