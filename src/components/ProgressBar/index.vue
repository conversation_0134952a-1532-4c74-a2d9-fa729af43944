<template>
  <div class="step-progress" v-if="props.size === 'default'">
    <div class="step-item" :class="item.status" v-for="item in progressList" :key="item.stepName">
      <div class="circle-wrapper">
        <div class="circle">
          <div class="circle-inner">
            <span v-if="item.percent !== 100">{{ item.percent }}%</span>
            <svg-icon v-else icon-name="icon-check" />
          </div>
        </div>
        <div class="step-name">{{ item.stepName }}</div>
      </div>
      <div class="line"></div>
    </div>
  </div>
  <div class="step-progress-small" v-else>
    <div v-for="(item, index) in progressList" :key="item.stepName" class="step-item">
      <div class="circle-wrapper">
        <div
          class="circle-inner"
          :class="{
            complete: item.status === 'success',
            active: item.status === 'process',
          }"
          v-if="item.percent !== 100"
        ></div>
        <CheckCircleFilled v-else style="color: #02b980" />
        <span
          class="percent-text"
          :class="{
            complete: item.status === 'success',
            active: item.status === 'process',
          }"
          >{{ item.percent }}%</span
        >
        <span class="step-name">{{ item.stepName }}</span>
      </div>
      <div v-if="index !== progressList.length - 1" class="line"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { CheckCircleFilled } from '@ant-design/icons-vue'

export interface ProgressItem {
  status: 'wait' | 'process' | 'success'
  percent: number
  stepName: string
}

const props = defineProps<{
  size: 'small' | 'default'
  progressList: ProgressItem[]
}>()
</script>

<style lang="scss" scoped>
// 小尺寸样式
.step-progress-small {
  display: flex;
  align-items: center;
  overflow-x: auto;
  padding-bottom: 4px;
  white-space: nowrap;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    height: 4px; // 滚动条高度
  }

  &::-webkit-scrollbar-track {
    background: transparent; // 滚动条轨道背景
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d9d9d9; // 滚动条滑块颜色
    border-radius: 2px;

    &:hover {
      background: #bfbfbf; // 鼠标悬停时的颜色
    }
  }

  &::-webkit-scrollbar-corner {
    background: transparent; // 滚动条角落
  }

  .step-item {
    display: flex;
    align-items: center;
    position: relative;
    flex-shrink: 0;

    .circle-wrapper {
      display: flex;
      align-items: center;
      gap: 4px;

      .circle-inner {
        width: 14px;
        height: 14px;
        border-radius: 50%;
        border: 4px solid #ccc;
        background-color: white;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        color: #ccc;
        flex-shrink: 0;

        &.complete {
          border-color: $primary-color;
        }

        &.active {
          border-color: #2f80ed;
          color: #2f80ed;
        }
      }

      .percent-text {
        font-weight: bold;

        &.complete {
          color: $primary-color;
        }

        &.active {
          color: #0078e7;
        }
      }

      .step-name {
        font-size: 14px;
        white-space: nowrap;
      }
    }
  }

  .line {
    width: 20px;
    height: 1px;
    background-color: #ccc;
    margin: 0 4px;
    flex-shrink: 0;
  }
}

// 默认高度样式
.step-progress {
  display: flex;
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  padding-bottom: 4px;

  // 自定义滚动条样式
  &::-webkit-scrollbar {
    height: 4px; // 滚动条高度
  }

  &::-webkit-scrollbar-track {
    background: transparent; // 滚动条轨道背景
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: #d9d9d9; // 滚动条滑块颜色
    border-radius: 2px;

    &:hover {
      background: #bfbfbf; // 鼠标悬停时的颜色
    }
  }

  &::-webkit-scrollbar-corner {
    background: transparent; // 滚动条角落
  }

  .step-item {
    display: flex;
    align-items: baseline;

    .line {
      width: 30px;
      border-bottom: 1px solid #bbb;
      position: relative;
      top: -4px;
      margin: 0 8px;
    }

    &:last-child {
      .line {
        display: none;
      }
    }

    &.wait .circle-wrapper .circle .circle-inner {
      border-color: #ccc;
    }
    &.process .circle-wrapper .circle .circle-inner {
      border-color: #0078e7;

      span {
        color: #0078e7;
      }
    }
    &.success .circle-wrapper .circle .circle-inner {
      border-color: $primary-color;
      color: $primary-color;

      span {
        color: $primary-color;
      }
    }

    .circle-wrapper {
      display: flex;
      flex-direction: column;
      align-items: center;
      position: relative;

      .circle {
        display: flex;
        align-items: center;

        .circle-inner {
          width: 40px;
          height: 40px;
          border: 3px solid #ccc;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;

          span {
            font-size: 12px;
            font-weight: bold;
            white-space: nowrap;
            vertical-align: middle;
          }
        }

        .line {
          width: 30px;
          border-bottom: 1px solid #bbb;
          margin: 0 8px;
        }
      }

      .step-name {
        font-size: 12px;
        font-weight: bold;
        max-width: 72px;
        color: #333;
        text-align: center;
        margin-top: 4px;
        line-height: 1.2;
      }
    }
  }
}
</style>
