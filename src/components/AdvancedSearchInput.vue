<template>
  <div class="advanced-search-input">
    <a-input
      v-model:value="searchValue"
      :placeholder="currentPlaceholder"
      class="search-input"
      @pressEnter="handleSearch"
      @input="handleInput"
    >
      <!-- 前缀：搜索类型下拉选择器 -->
      <template #prefix>
        <a-dropdown v-model:open="dropdownVisible" :trigger="['click']">
          <div
            class="type-selector-btn"
            :class="{ 'type-selector-btn-active': dropdownVisible }"
            @click="dropdownVisible = !dropdownVisible"
          >
            <DownOutlined style="font-size: 12px" />
          </div>
          <template #overlay>
            <a-menu @click="handleTypeSelect" :selectedKeys="[selectedType]">
              <a-menu-item v-for="option in searchOptions" :key="option.value">
                {{ option.label }}
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </template>

      <!-- 后缀：搜索按钮 -->
      <template #suffix>
        <a-button
          type="primary"
          class="search-btn"
          :icon="h(SearchOutlined)"
          @click="handleSearch"
        />
      </template>
    </a-input>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, watch } from 'vue'
import { DownOutlined, SearchOutlined } from '@ant-design/icons-vue'

// 定义搜索选项接口
interface SearchOption {
  label: string
  value: string
  placeholder?: string
}

// 定义组件属性
interface Props {
  searchOptions?: SearchOption[]
  defaultType?: string
  placeholder?: string
  modelValue?: string
}

// 定义事件
interface Emits {
  (e: 'search', data: { type: string; value: string }): void
  (e: 'update:modelValue', value: string): void
  (e: 'typeChange', type: string): void
}

const props = withDefaults(defineProps<Props>(), {
  searchOptions: () => [
    {
      label: '工单编号、产品编号、产品名称、产品规格',
      value: 'all',
      placeholder: '输入产品编号搜索',
    },
    { label: '工单编号', value: 'workOrderNo', placeholder: '输入工单编号搜索' },
    { label: '产品编号', value: 'productNo', placeholder: '输入产品编号搜索' },
    { label: '产品名称', value: 'productName', placeholder: '输入产品名称搜索' },
    { label: '产品规格', value: 'productSpec', placeholder: '输入产品规格搜索' },
    { label: '工单备注', value: 'workOrderNote', placeholder: '输入工单备注搜索' },
    { label: '紧急程度', value: 'urgency', placeholder: '输入紧急程度搜索' },
    { label: '压铸机', value: 'machine', placeholder: '输入压铸机搜索' },
  ],
  defaultType: 'all',
  placeholder: '输入产品编号搜索',
})

const emit = defineEmits<Emits>()

// 响应式数据
const dropdownVisible = ref(false)
const selectedType = ref(props.defaultType)
const searchValue = ref(props.modelValue || '')

// 计算属性
const currentPlaceholder = computed(() => {
  const option = props.searchOptions.find((opt) => opt.value === selectedType.value)
  return option?.placeholder || props.placeholder
})

// 事件处理函数
const handleTypeSelect = ({ key }: { key: string }) => {
  selectedType.value = key
  dropdownVisible.value = false
  emit('typeChange', key)
}

const handleInput = (e: Event) => {
  const target = e.target as HTMLInputElement
  searchValue.value = target.value
  emit('update:modelValue', target.value)
}

const handleSearch = () => {
  emit('search', {
    type: selectedType.value,
    value: searchValue.value.trim(),
  })
}

// 监听外部 modelValue 变化
watch(
  () => props.modelValue,
  (newValue) => {
    if (newValue !== searchValue.value) {
      searchValue.value = newValue || ''
    }
  },
)
</script>

<style lang="scss" scoped>
.advanced-search-input {
  justify-content: center;
  align-items: center;
  height: 32px;
  display: flex;

  .search-input {
    box-sizing: border-box;
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 6px;
    width: 16vw;
    height: 32px;
    padding: 0;

    .type-selector-btn {
      width: 30px;
      height: 100%;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;

      &::after {
        content: '';
        background-color: #e5e5e5;
        width: 1px;
        height: 16px;
        margin: auto;
        display: block;
        position: absolute;
        top: 0;
        bottom: 0;
        right: 0;
      }
    }

    .search-btn {
      border-radius: 0 6px 6px 0;
    }
  }
}
</style>
