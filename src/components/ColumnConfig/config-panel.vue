<template>
  <div class="column-config-panel">
    <!-- 1. 字段数量统计 -->
    <div class="header">字段配置 {{ visibleColumnCount }}/{{ columns.length }}</div>

    <!-- 2. 搜索字段 -->
    <a-input
      v-model:value="searchKeyword"
      placeholder="搜索字段"
      allow-clear
      class="search-input"
    />

    <!-- 3. 拖拽排序 -->
    <draggable
      v-model="sortedColumns"
      group="columns"
      handle=".drag-handle"
      :disabled="!draggableEnabled"
      :move="onDragMove"
      @end="onDragEnd"
      item-key="key"
      class="field-list"
    >
      <template #item="{ element }">
        <div
          class="field-item"
          :class="{
            fixed: isFixed(element),
            hidden: !element.visible,
            disabled: isDisabled(element),
          }"
        >
          <div class="field-item-left">
            <!-- 拖拽图标 -->
            <span
              :class="{
                'drag-handle': canDrag(element),
                'drag-handle--disabled': !canDrag(element),
              }"
            >
              <HolderOutlined />
            </span>

            <!-- 字段名 -->
            <span class="field-name">{{ element.title }}</span>
          </div>

          <div class="actions">
            <!-- 固定/取消固定 -->
            <a-button
              type="text"
              size="small"
              :class="{ 'pin-fixed': isFixed(element) }"
              @click="toggleFixed(element)"
              :disabled="!element.visible"
            >
              <PushpinOutlined />
            </a-button>

            <!-- 显示/隐藏 -->
            <a-button type="text" size="small" @click="toggleVisible(element)">
              <component :is="element.visible ? EyeOutlined : EyeInvisibleOutlined" />
            </a-button>
          </div>
        </div>
      </template>
    </draggable>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import draggable from 'vuedraggable'
import {
  HolderOutlined,
  PushpinOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
} from '@ant-design/icons-vue'

import type { TableColumn } from '@/common/types'
import { useTableColumns } from '@/composables/useTableColumns'

const props = defineProps<{
  columns: TableColumn[]
}>()

const emit = defineEmits<{
  (e: 'update', value: TableColumn[]): void
}>()

const searchKeyword = ref('')

// clone prop columns
const sortedColumns = ref<TableColumn[]>([...props.columns])

// 实时统计显示字段数量
const { visibleColumnCount } = useTableColumns(sortedColumns)

// 是否允许拖拽（全局开启）
const draggableEnabled = true

// 判断字段是否固定
const isFixed = (col: TableColumn) => col.fixed === true || col.fixed === 'left'

// 判断字段是否为禁用（如操作列）
const isDisabled = (col: TableColumn) => col.key === 'action'

// 允许拖拽的字段：非固定
const canDrag = (col: TableColumn) => !isFixed(col)

// 拖拽规则限制（不能拖动固定列、不能拖到固定列前）
function onDragMove(e: any) {
  const { draggedContext, relatedContext } = e
  const dragged = draggedContext.element
  const related = relatedContext.element

  if (isFixed(dragged) || isFixed(related)) return false
  if (isDisabled(dragged) || isDisabled(related)) return false
  return true
}

function onDragEnd() {
  emit('update', sortedColumns.value)
}

// 切换显示/隐藏
function toggleVisible(col: TableColumn) {
  col.visible = !col.visible

  emit('update', sortedColumns.value)
}

// 切换固定/取消固定
function toggleFixed(col: TableColumn) {
  if (!col.visible) return
  col.fixed = isFixed(col) ? false : 'left'
  reorderByFixed()
  emit('update', sortedColumns.value)
}

// 根据固定状态自动重排
function reorderByFixed() {
  const fixed = sortedColumns.value.filter((c) => isFixed(c) && !isDisabled(c))
  const nonFixed = sortedColumns.value.filter((c) => !isFixed(c) && !isDisabled(c))
  const disabled = sortedColumns.value.filter((c) => isDisabled(c))
  sortedColumns.value = [...fixed, ...nonFixed, ...disabled]
}

// 监听 props.columns 更新内部排序副本
// watch(
//   () => props.columns,
//   (newVal) => {
//     console.log('config-panel-props.columns', newVal)
//     sortedColumns.value = [...newVal]
//   },
//   { immediate: true, deep: true },
// )
</script>

<style lang="scss" scoped>
.column-config-panel {
  padding: 16px;
  width: 300px;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header {
  font-weight: bold;
  margin-bottom: 12px;
}

.search-input {
  margin-bottom: 12px;
}

.field-list {
  max-height: 400px;
  overflow-y: auto;
}

.field-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 6px 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.field-item:hover {
  background-color: #f5f5f5;
}

.field-item.fixed .drag-handle--disabled,
.field-item.hidden .field-name {
  color: #bbb;
}

.field-item.disabled {
  pointer-events: none;
  opacity: 0.4;
}

.drag-handle,
.drag-handle--disabled {
  cursor: move;
  margin-right: 8px;
  font-size: 16px;
  font-weight: bolder;
}

.pin-fixed {
  color: $primary-color;
}
.pin-fixed:hover {
  color: $primary-color;
}

// 滚动条样式
.field-list::-webkit-scrollbar {
  width: 6px;
}

.field-list::-webkit-scrollbar-track {
  background: #f5f5f5;
  border-radius: 3px;
}

.field-list::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;

  &:hover {
    background: #bfbfbf;
  }
}
</style>
