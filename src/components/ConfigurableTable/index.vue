<template>
  <div class="configurable-table">
    <div class="table-toolbar-wrapper">
      <div class="table-toolbar-left">
        <a-space>
          <slot name="toolbar-left"></slot>
          <!-- 列配置组件 -->
          <ColumnConfig
            v-if="showColumnConfig"
            :columns="props.columns"
            @changeVisibleColumns="handleColumnsUpdate"
          />
          <!-- 行高配置组件 -->
          <LineHeight v-if="showLineHeight" @change="handleLineHeightChange" />
        </a-space>
      </div>
      <div class="table-toolbar-right">
        <a-space>
          <slot name="toolbar-right"></slot>
        </a-space>
      </div>
    </div>
    <!-- 表格主体 -->
    <a-table
      :row-selection="tableRowSelection"
      :scroll="scroll"
      :columns="tableColumns"
      :data-source="dataSource"
      :loading="loading"
      :customRow="setRowStyle"
      :pagination="tablePagination"
      bordered
      v-bind="$attrs"
    >
      <!-- 传递所有插槽 -->
      <template v-for="(_, name) in $slots" #[name]="slotData">
        <slot :name="name" v-bind="slotData" />
      </template>

      <!-- 默认操作列 -->
      <template #bodyCell="{ column, record, index }">
        <slot name="bodyCell" :column="column" :record="record" :index="index"> </slot>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import type { TableColumn } from '@/common/types'
import { useTableConfig } from './useTableConfig'
import ColumnConfig from '../ColumnConfig/index.vue'
import LineHeight from '../LineHeight/index.vue'

interface Props {
  columns: TableColumn[] // 表格列配置
  dataSource: any[] // 数据源
  scroll?: { x?: number; y?: number } // 滚动
  showColumnConfig?: boolean // 是否显示列配置
  showLineHeight?: boolean // 是否显示行高配置
  rowSelection?: boolean | Object // 是否支持行选择
  loading?: boolean // 加载状态
  pagination?: any // 分页配置
}

const props = withDefaults(defineProps<Props>(), {
  showColumnConfig: true,
  showLineHeight: true,
  rowSelection: false,
  scroll: () => ({ x: 3200, y: 600 }),
  pagination: () => ({
    showTotal: (total: number) => `${total} 条记录`,
    showSizeChanger: true,
    showQuickJumper: true,
  }),
})
const emit = defineEmits(['lineHeightChange'])

const {
  columns: tableColumns,
  updateColumns,
  setRowStyle,
  updateLineHeight,
} = useTableConfig(props.columns)

// 计算属性：分页配置
const tablePagination = computed(() => {
  return props.pagination
})
// 计算属性：行选择配置
const tableRowSelection = computed(() => {
  if (typeof props.rowSelection === 'object') {
    return props.rowSelection
  }

  return null
})

/**
 * 处理列配置更新
 */
const handleColumnsUpdate = (newColumns: TableColumn[]) => {
  updateColumns(newColumns)
}

/**
 * 处理行高变化
 */
const handleLineHeightChange = (height: string) => {
  updateLineHeight(height as any)
  emit('lineHeightChange', height)
}
</script>

<style lang="scss" scoped>
.table-toolbar-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: $spacing-md;

  .table-toolbar-left {
    display: flex;
  }
}
</style>
