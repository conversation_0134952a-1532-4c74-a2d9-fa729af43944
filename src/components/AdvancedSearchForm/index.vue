<template>
  <div class="advanced-search-form">
    <div class="form-wrapper">
      <div class="filter-list">
        <a-form
          ref="formRef"
          :name="formName"
          label-align="right"
          :label-col="{ style: { width: '108px' } }"
          :colon="false"
          class="ant-advanced-search-form"
          v-bind="formProps"
        >
          <a-row :gutter="gutter">
            <template v-for="i in actualTotalItems" :key="i">
              <a-col v-show="expand || i <= collapsedItems" :span="colSpan">
                <slot :name="`item-${i}`" :index="i" :expanded="expand" :form-ref="formRef">
                  <!-- 默认插槽内容 -->
                  <a-form-item :name="`field-${i}`" :label="`Field ${i}`" :rules="defaultRules">
                    <a-input :placeholder="`请输入 Field ${i}`" />
                  </a-form-item>
                </slot>
              </a-col>
            </template>
          </a-row>
        </a-form>
      </div>
      <div class="form-actions">
        <slot name="actions" :on-search="handleSearch" :form-ref="formRef" :expand="expand">
          <!-- 默认操作按钮 -->
          <a-button type="primary" @click="handleSearch">查询</a-button>
          <a-button :icon="h(SearchOutlined)" @click="handleReset" />
          <a-button :icon="h(RedoOutlined)" @click="handleExport" />
          <a-button :icon="h(MoreOutlined)" @click="handleMore" />
        </slot>
      </div>
    </div>
    <div class="expand-wrapper" v-if="showExpandToggle && actualTotalItems > collapsedItems">
      <div class="expand-line"></div>
      <div class="expand-icon" @click="toggleExpand">
        <DownOutlined :rotate="expand ? 180 : 0" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, h, computed, useSlots } from 'vue'
import { SearchOutlined, DownOutlined, RedoOutlined, MoreOutlined } from '@ant-design/icons-vue'
import type { FormInstance, Rule } from 'ant-design-vue/es/form'

// 定义组件 Props 接口
export interface AdvancedSearchFormProps {
  /** 表单项总数（可选，不传入时自动根据插槽数量确定） */
  totalItems?: number
  /** 收起状态下显示的表单项数量 */
  collapsedItems?: number
  /** 每行显示的列数（对应 ant-design-vue 的 span 值：24/colNumber） */
  colsPerRow?: number
  /** 列间距 */
  gutter?: number
  /** 表单名称 */
  formName?: string
  /** 是否显示展开/收起切换 */
  showExpandToggle?: boolean
  /** 初始展开状态 */
  initialExpanded?: boolean
  /** 默认表单验证规则 */
  defaultRules?: Rule[]
  /** 传递给 a-form 的额外属性 */
  formProps?: Record<string, any>
}

// 定义组件事件接口
export interface AdvancedSearchFormEmits {
  /** 查询事件 */
  search: [formData: Record<string, any>]
  /** 重置事件 */
  reset: []
  /** 导出事件 */
  export: []
  /** 更多操作事件 */
  more: []
  /** 展开状态变化事件 */
  'expand-change': [expanded: boolean]
}

// Props 定义
const props = withDefaults(defineProps<AdvancedSearchFormProps>(), {
  totalItems: undefined, // 改为 undefined，让组件自动检测
  collapsedItems: 3,
  colsPerRow: 3,
  gutter: 24,
  formName: 'advanced_search',
  showExpandToggle: true,
  initialExpanded: false,
  defaultRules: () => [{ required: true, message: '请输入内容' }],
  formProps: () => ({}),
})

// 事件定义
const emit = defineEmits<AdvancedSearchFormEmits>()

// 获取插槽
const slots = useSlots()

// 响应式数据
const formRef = ref<FormInstance>()
const expand = ref(props.initialExpanded)

// 计算属性
const colSpan = computed(() => Math.floor(24 / props.colsPerRow))

// 自动检测表单项数量
const actualTotalItems = computed(() => {
  // 如果手动传入了 totalItems，优先使用
  if (props.totalItems !== undefined) {
    return props.totalItems
  }

  // 否则自动检测插槽数量
  const itemSlots = Object.keys(slots).filter((slotName) => {
    // 匹配 item-数字 格式的插槽
    return /^item-\d+$/.test(slotName)
  })

  if (itemSlots.length === 0) {
    // 如果没有传入任何 item 插槽，返回默认值
    return 10
  }

  // 提取所有插槽的数字并找到最大值
  const slotNumbers = itemSlots.map((slotName) => {
    const match = slotName.match(/^item-(\d+)$/)
    return match ? parseInt(match[1], 10) : 0
  })

  return Math.max(...slotNumbers)
})

// 方法
const toggleExpand = () => {
  expand.value = !expand.value
  emit('expand-change', expand.value)
}

const handleSearch = async () => {
  try {
    const values = await formRef.value?.validateFields()
    emit('search', values || {})
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

const handleReset = () => {
  formRef.value?.resetFields()
  emit('reset')
}

const handleExport = () => {
  emit('export')
}

const handleMore = () => {
  emit('more')
}

// 暴露组件实例方法
defineExpose({
  formRef,
  expand,
  toggleExpand,
  handleSearch,
  handleReset,
  validateFields: () => formRef.value?.validateFields(),
  resetFields: () => formRef.value?.resetFields(),
  setFieldValue: (name: string, value: any) => {
    // 由于 FormInstance 类型限制，我们提供一个简单的包装方法
    // 实际使用时建议直接操作表单数据
    console.warn('setFieldValue: 请考虑使用 getFieldsValue/setFieldsValue 或直接操作表单数据')
  },
  getFieldsValue: () => formRef.value?.getFieldsValue(),
})
</script>

<style lang="scss" scoped>
.advanced-search-form {
  .form-wrapper {
    justify-content: space-between;
    align-items: flex-start;
    width: 100%;
    display: flex;

    .filter-list {
      flex: 1;
      overflow: hidden;
    }

    .form-actions {
      align-items: center;
      gap: 12px;
      display: flex;
      margin-left: 10px;
    }
  }

  .expand-wrapper {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: -$spacing-sm;
    margin-left: -$spacing-lg;
    margin-right: -$spacing-lg;

    .expand-line {
      position: absolute;
      width: 100%;
      height: 1px;
      background-color: #e8e8e8;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }

    .expand-icon {
      position: relative;
      z-index: 1;
      background-color: #fff;
      width: 40px;
      height: 14px;
      color: #666;
      border: 1px solid #eee;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      border-radius: 8px;
      transition: all 0.3s ease;

      &:hover {
        color: $primary-color;
        border-color: $primary-color;
      }
    }
  }
}
</style>
