# AdvancedSearchForm 高级搜索表单组件

一个功能强大、可高度自定义的高级搜索表单组件，支持展开/收起功能、自定义表单项内容和操作按钮。

## 特性

- 🎯 **高度可配置**：支持自定义表单项数量、每行显示数量等
- 🔧 **插槽系统**：通过插槽自定义表单项内容和操作按钮
- 🤖 **智能检测**：自动检测插槽数量，无需手动指定表单项总数
- 📱 **响应式设计**：自适应不同屏幕尺寸
- 🎨 **展开收起**：内置展开/收起功能，节省页面空间
- 🔍 **表单验证**：完整的表单验证支持
- 📝 **TypeScript**：完整的 TypeScript 类型支持

## 安装使用

```vue
<script setup lang="ts">
import AdvancedSearchForm from '@/components/AdvancedSearchForm/index.vue'
</script>

<template>
  <AdvancedSearchForm />
</template>
```

## API 参考

### Props

| 属性名             | 类型                  | 默认值                                        | 说明                                             |
| ------------------ | --------------------- | --------------------------------------------- | ------------------------------------------------ |
| `totalItems`       | `number`              | `自动检测`                                    | 表单项总数（可选，不传入时自动根据插槽数量确定） |
| `collapsedItems`   | `number`              | `3`                                           | 收起状态下显示的表单项数量                       |
| `colsPerRow`       | `number`              | `3`                                           | 每行显示的列数                                   |
| `gutter`           | `number`              | `24`                                          | 列间距                                           |
| `formName`         | `string`              | `'advanced_search'`                           | 表单名称                                         |
| `showExpandToggle` | `boolean`             | `true`                                        | 是否显示展开/收起切换                            |
| `initialExpanded`  | `boolean`             | `false`                                       | 初始展开状态                                     |
| `defaultRules`     | `Rule[]`              | `[{ required: true, message: '请输入内容' }]` | 默认表单验证规则                                 |
| `formProps`        | `Record<string, any>` | `{}`                                          | 传递给 a-form 的额外属性                         |

### Events

| 事件名          | 参数                              | 说明                   |
| --------------- | --------------------------------- | ---------------------- |
| `search`        | `(formData: Record<string, any>)` | 查询事件，返回表单数据 |
| `reset`         | `()`                              | 重置事件               |
| `export`        | `()`                              | 导出事件               |
| `more`          | `()`                              | 更多操作事件           |
| `expand-change` | `(expanded: boolean)`             | 展开状态变化事件       |

### Slots

#### 表单项插槽 `item-{index}`

每个表单项都有对应的插槽，可以自定义内容。

```vue
<template #item-1="{ index, expanded, formRef }">
  <a-form-item name="workOrderNo" label="工单编号">
    <a-input placeholder="请输入工单编号" />
  </a-form-item>
</template>
```

**插槽参数：**

- `index: number` - 当前表单项索引
- `expanded: boolean` - 当前展开状态
- `formRef: FormInstance` - 表单实例引用

#### 操作按钮插槽 `actions`

自定义操作按钮区域。

```vue
<template #actions="{ onSearch, formRef, expand }">
  <a-button type="primary" @click="onSearch">搜索</a-button>
  <a-button @click="handleCustomAction">自定义操作</a-button>
</template>
```

**插槽参数：**

- `onSearch: () => void` - 搜索方法
- `formRef: FormInstance` - 表单实例引用
- `expand: boolean` - 当前展开状态

### 暴露的方法

通过 `ref` 可以访问以下方法：

| 方法名           | 参数                         | 返回值                | 说明              |
| ---------------- | ---------------------------- | --------------------- | ----------------- |
| `validateFields` | `()`                         | `Promise<any>`        | 验证表单字段      |
| `resetFields`    | `()`                         | `void`                | 重置表单字段      |
| `setFieldValue`  | `(name: string, value: any)` | `void`                | 设置单个字段值    |
| `getFieldsValue` | `()`                         | `Record<string, any>` | 获取所有字段值    |
| `toggleExpand`   | `()`                         | `void`                | 切换展开/收起状态 |
| `handleSearch`   | `()`                         | `void`                | 执行搜索          |
| `handleReset`    | `()`                         | `void`                | 执行重置          |

## 使用示例

### 基础用法

```vue
<template>
  <!-- 自动检测插槽数量，无需手动指定 totalItems -->
  <AdvancedSearchForm
    :collapsed-items="2"
    @search="handleSearch"
    @expand-change="handleExpandChange"
  >
    <template #item-1>
      <a-form-item name="field1" label="字段1">
        <a-input placeholder="请输入字段1" />
      </a-form-item>
    </template>
    <template #item-2>
      <a-form-item name="field2" label="字段2">
        <a-input placeholder="请输入字段2" />
      </a-form-item>
    </template>
    <!-- 会自动检测到有2个表单项 -->
  </AdvancedSearchForm>
</template>

<script setup lang="ts">
const handleSearch = (formData: Record<string, any>) => {
  console.log('搜索数据:', formData)
}

const handleExpandChange = (expanded: boolean) => {
  console.log('展开状态:', expanded)
}
</script>
```

### 自定义表单项

```vue
<template>
  <AdvancedSearchForm :total-items="5" :collapsed-items="3" @search="handleSearch">
    <template #item-1="{ formRef }">
      <a-form-item name="workOrderNo" label="工单编号" :rules="workOrderRules">
        <a-input placeholder="请输入工单编号" />
      </a-form-item>
    </template>

    <template #item-2="{ formRef }">
      <a-form-item name="productName" label="产品名称">
        <a-select placeholder="请选择产品名称" :options="productOptions" />
      </a-form-item>
    </template>

    <template #item-3="{ formRef }">
      <a-form-item name="dateRange" label="日期范围">
        <a-range-picker />
      </a-form-item>
    </template>

    <template #item-4="{ formRef }">
      <a-form-item name="status" label="状态">
        <a-checkbox-group :options="statusOptions" />
      </a-form-item>
    </template>

    <template #item-5="{ formRef }">
      <a-form-item name="priority" label="优先级">
        <a-radio-group :options="priorityOptions" />
      </a-form-item>
    </template>
  </AdvancedSearchForm>
</template>

<script setup lang="ts">
import type { SelectProps, CheckboxProps, RadioProps } from 'ant-design-vue'

const workOrderRules = [
  { required: true, message: '请输入工单编号' },
  { pattern: /^WO\d{6}$/, message: '工单编号格式错误' },
]

const productOptions: SelectProps['options'] = [
  { label: '产品A', value: 'productA' },
  { label: '产品B', value: 'productB' },
]

const statusOptions: CheckboxProps[] = [
  { label: '待处理', value: 'pending' },
  { label: '进行中', value: 'processing' },
  { label: '已完成', value: 'completed' },
]

const priorityOptions: RadioProps[] = [
  { label: '低', value: 'low' },
  { label: '中', value: 'medium' },
  { label: '高', value: 'high' },
]

const handleSearch = (formData: Record<string, any>) => {
  console.log('搜索表单数据:', formData)
  // 执行搜索逻辑
}
</script>
```

### 自定义操作按钮

```vue
<template>
  <AdvancedSearchForm @search="handleSearch">
    <!-- 表单项插槽... -->

    <template #actions="{ onSearch, formRef }">
      <a-button type="primary" @click="onSearch">
        <SearchOutlined />
        查询
      </a-button>
      <a-button @click="handleReset(formRef)">
        <RedoOutlined />
        重置
      </a-button>
      <a-button @click="handleExport">
        <DownloadOutlined />
        导出
      </a-button>
      <a-dropdown>
        <template #overlay>
          <a-menu>
            <a-menu-item @click="handleBatchDelete">批量删除</a-menu-item>
            <a-menu-item @click="handleBatchUpdate">批量更新</a-menu-item>
          </a-menu>
        </template>
        <a-button>
          更多操作
          <DownOutlined />
        </a-button>
      </a-dropdown>
    </template>
  </AdvancedSearchForm>
</template>

<script setup lang="ts">
import { SearchOutlined, RedoOutlined, DownloadOutlined, DownOutlined } from '@ant-design/icons-vue'
import type { FormInstance } from 'ant-design-vue/es/form'

const handleSearch = (formData: Record<string, any>) => {
  console.log('搜索:', formData)
}

const handleReset = (formRef: FormInstance) => {
  formRef.resetFields()
}

const handleExport = () => {
  console.log('导出数据')
}

const handleBatchDelete = () => {
  console.log('批量删除')
}

const handleBatchUpdate = () => {
  console.log('批量更新')
}
</script>
```

### 使用组件引用

```vue
<template>
  <div>
    <AdvancedSearchForm ref="searchFormRef" @search="handleSearch">
      <!-- 表单项内容... -->
    </AdvancedSearchForm>

    <div class="external-controls">
      <a-button @click="manualSearch">手动触发搜索</a-button>
      <a-button @click="resetForm">重置表单</a-button>
      <a-button @click="toggleExpand">切换展开</a-button>
      <a-button @click="fillTestData">填充测试数据</a-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const searchFormRef = ref()

const handleSearch = (formData: Record<string, any>) => {
  console.log('搜索结果:', formData)
}

const manualSearch = () => {
  searchFormRef.value?.handleSearch()
}

const resetForm = () => {
  searchFormRef.value?.resetFields()
}

const toggleExpand = () => {
  searchFormRef.value?.toggleExpand()
}

const fillTestData = () => {
  searchFormRef.value?.setFieldValue('field-1', '测试数据1')
  searchFormRef.value?.setFieldValue('field-2', '测试数据2')
}
</script>
```

### 响应式配置

```vue
<template>
  <AdvancedSearchForm
    :collapsed-items="collapsedItems"
    :cols-per-row="colsPerRow"
    :gutter="gutter"
    @search="handleSearch"
  >
    <!-- 8个表单项会被自动检测，无需手动指定 totalItems -->
    <template #item-1>
      <a-form-item name="field1" label="字段1">
        <a-input />
      </a-form-item>
    </template>
    <!-- ... 更多表单项 ... -->
    <template #item-8>
      <a-form-item name="field8" label="字段8">
        <a-input />
      </a-form-item>
    </template>
  </AdvancedSearchForm>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useBreakpoint } from '@/composables/useBreakpoint'

const { isXs, isSm, isMd } = useBreakpoint()

// 根据屏幕尺寸动态调整配置
const collapsedItems = computed(() => {
  if (isXs.value) return 1
  if (isSm.value) return 2
  return 3
})

const colsPerRow = computed(() => {
  if (isXs.value) return 1
  if (isSm.value) return 2
  if (isMd.value) return 2
  return 3
})

const gutter = computed(() => {
  if (isXs.value) return 16
  return 24
})

const handleSearch = (formData: Record<string, any>) => {
  console.log('搜索:', formData)
}
</script>
```

## 设计思路

### 1. 组件设计原则

- **单一职责**：专注于高级搜索表单的展示和交互
- **高内聚低耦合**：内部逻辑完整，对外接口简洁
- **可扩展性**：通过插槽系统支持无限扩展
- **类型安全**：完整的 TypeScript 类型定义

### 2. 插槽设计

- **表单项插槽**：每个表单项独立插槽，支持完全自定义
- **操作按钮插槽**：自定义操作区域，保持布局一致性
- **作用域插槽**：提供必要的上下文信息，增强灵活性

### 3. 状态管理

- **内部状态**：展开/收起状态由组件内部管理
- **表单状态**：依托 ant-design-vue 的表单系统
- **事件通信**：通过事件向父组件传递状态变化

## 最佳实践

### 1. 表单项设计

```vue
<!-- ✅ 推荐：语义化的表单项名称 -->
<template #item-1>
  <a-form-item name="workOrderNo" label="工单编号">
    <a-input placeholder="请输入工单编号" />
  </a-form-item>
</template>

<!-- ❌ 避免：无意义的通用名称 -->
<template #item-1>
  <a-form-item name="field1" label="字段1">
    <a-input placeholder="输入内容" />
  </a-form-item>
</template>
```

### 2. 验证规则设置

```vue
<!-- ✅ 推荐：合理的验证规则 -->
<template #item-1>
  <a-form-item
    name="email"
    label="邮箱"
    :rules="[
      { required: true, message: '请输入邮箱' },
      { type: 'email', message: '邮箱格式不正确' },
    ]"
  >
    <a-input placeholder="请输入邮箱地址" />
  </a-form-item>
</template>
```

### 3. 响应式布局

```vue
<!-- ✅ 推荐：根据屏幕尺寸调整布局 -->
<AdvancedSearchForm
  :cols-per-row="isMobile ? 1 : 3"
  :collapsed-items="isMobile ? 1 : 3"
  :gutter="isMobile ? 16 : 24"
/>
```

### 4. 事件处理

```vue
<script setup lang="ts">
// ✅ 推荐：明确的事件处理
const handleSearch = async (formData: Record<string, any>) => {
  try {
    loading.value = true
    const result = await searchAPI(formData)
    updateTableData(result)
  } catch (error) {
    message.error('搜索失败')
  } finally {
    loading.value = false
  }
}
</script>
```

## 注意事项

1. **插槽命名**：表单项插槽必须按 `item-{index}` 格式命名，从 1 开始连续编号
2. **自动检测**：组件会自动检测最大的插槽编号来确定表单项总数
3. **手动覆盖**：如果需要手动指定总数，可以传入 `totalItems` 属性来覆盖自动检测
4. **表单验证**：每个自定义表单项都应该设置合适的验证规则
5. **性能优化**：大量表单项时考虑使用虚拟滚动或分页
6. **无障碍访问**：确保表单项有正确的 label 和 aria 属性
7. **移动端适配**：在小屏幕设备上适当调整布局参数

## 常见问题

### Q: 组件如何自动检测表单项数量？

A: 组件会扫描所有传入的插槽，找到 `item-{数字}` 格式的插槽，然后取最大编号作为总数：

```vue
<!-- 自动检测到5个表单项 -->
<AdvancedSearchForm>
  <template #item-1>...</template>
  <template #item-3>...</template>  <!-- 可以跳号 -->
  <template #item-5>...</template>  <!-- 最大编号是5 -->
</AdvancedSearchForm>
```

### Q: 如何动态控制表单项的显示/隐藏？

A: 可以在插槽内部使用 `v-if` 或 `v-show` 指令：

```vue
<template #item-2="{ expanded }">
  <a-form-item v-if="showAdvancedField" name="advanced" label="高级选项">
    <a-input />
  </a-form-item>
</template>
```

### Q: 如何实现表单项之间的联动？

A: 通过组件引用获取表单实例，监听字段变化：

```vue
<script setup lang="ts">
import { watch } from 'vue'

const searchFormRef = ref()

watch(
  () => formData.type,
  (newType) => {
    if (newType === 'special') {
      searchFormRef.value?.setFieldValue('category', '')
    }
  },
)
</script>
```

### Q: 如何自定义展开/收起的触发逻辑？

A: 可以通过 `showExpandToggle={false}` 隐藏默认按钮，然后使用组件引用手动控制：

```vue
<template>
  <AdvancedSearchForm ref="formRef" :show-expand-toggle="false" />
  <a-button @click="formRef?.toggleExpand()"> 自定义展开按钮 </a-button>
</template>
```
