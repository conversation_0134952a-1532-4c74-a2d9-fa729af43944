<template>
  <a-tag
    :bordered="false"
    :color="bgColor"
    :style="{
      color: textColor,
    }"
    class="custom-tag-wrapper"
  >
    <span class="custom-tag-text">{{ text }}</span>
  </a-tag>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

const props = defineProps<{
  color: string
  text: string
}>()

// 配色映射表
const colorMap: Record<string, string> = {
  wait: '#000000',
  processing: '#1790ff',
  success: '#02b981',
  warn: '#faad14',
  error: '#ff5950',
}

// 判断传入的是关键字还是直接颜色值
const resolvedColor = computed(() => {
  return colorMap[props.color] || props.color
})

// 字体颜色：使用 resolvedColor
const textColor = computed(() => resolvedColor.value)

// 背景色：透明度 0.1 的 resolvedColor
const bgColor = computed(() => convertToRgba(resolvedColor.value, 0.1))

// 16进制转 RGBA
function convertToRgba(hex: string, alpha: number): string {
  let c = hex.replace('#', '')
  if (c.length === 3) {
    c = c
      .split('')
      .map((ch) => ch + ch)
      .join('')
  }
  if (c.length !== 6) {
    return hex // fallback
  }

  const r = parseInt(c.slice(0, 2), 16)
  const g = parseInt(c.slice(2, 4), 16)
  const b = parseInt(c.slice(4, 6), 16)

  return `rgba(${r}, ${g}, ${b}, ${alpha})`
}
</script>

<style lang="scss" scoped>
.custom-tag-text {
  white-space: nowrap;
  text-overflow: ellipsis;
  font-size: 13px;
  font-weight: 600;
  line-height: 20px;
  display: inline-block;
  overflow: hidden;
}

.custom-tag-wrapper {
  display: flex;
  align-items: center;
  width: fit-content;
  padding: 0 10px;
  border-radius: 13px;
}
</style>
