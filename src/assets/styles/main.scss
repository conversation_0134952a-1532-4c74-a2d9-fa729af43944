#app {
  height: 100vh;
  width: 100vw;
  font-weight: normal;
  display: grid;
  padding: 0;
}

// 修改ant-table表格padding
.ant-table-wrapper .ant-table-tbody > tr > td {
  padding: 6px 8px;
}
.ant-table-wrapper .ant-table-thead>tr>th {
  padding: 8px 4px 8px 8px;
}

// 重置弹窗样式
.m-modal .ant-modal-content {
  padding: 0 !important;

  .ant-modal-header {
    color: rgba(0, 0, 0, .85);
    background: #fff;
    border-bottom: 1px solid rgba(0, 0, 0, .06);
    border-radius: 12px 12px 0 0;
    padding: 16px 24px;
    margin-bottom: 0;
  }

  .ant-modal-footer {
    text-align: right;
    border-top: 1px solid rgba(0, 0, 0, .06);
    border-radius: 0 0 12px 12px;
    padding: 16px 24px;
    margin-top: 0;
  }
}
