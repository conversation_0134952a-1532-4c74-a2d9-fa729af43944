:root {
  // Primary color system
  --primary: #02b980;
  --primary-50: #e8fdf5;
  --primary-100: #c5f9e1;
  --primary-200: #8ff3c8;
  --primary-300: #59e8af;
  --primary-400: #3dd5a8;
  --primary-500: #02b980;
  --primary-600: #01a16f;
  --primary-700: #018a5e;
  --primary-800: #017248;
  --primary-900: #015b3a;

  // Secondary colors
  --secondary: #6c757d;
  --secondary-50: #f8f9fa;
  --secondary-100: #e9ecef;
  --secondary-200: #dee2e6;
  --secondary-300: #ced4da;
  --secondary-400: #adb5bd;
  --secondary-500: #6c757d;
  --secondary-600: #5a6268;
  --secondary-700: #495057;
  --secondary-800: #343a40;
  --secondary-900: #212529;

  // Success colors
  --success: #28a745;
  --success-50: #f0f9f3;
  --success-100: #dcf4e4;
  --success-200: #bde9cc;
  --success-300: #95d5ab;
  --success-400: #5cbf83;
  --success-500: #28a745;
  --success-600: #20923a;
  --success-700: #1e7e34;
  --success-800: #1a652a;
  --success-900: #155724;

  // Warning colors
  --warning: #ffc107;
  --warning-50: #fffbf0;
  --warning-100: #fff3cd;
  --warning-200: #ffe69c;
  --warning-300: #ffda6a;
  --warning-400: #ffcd39;
  --warning-500: #ffc107;
  --warning-600: #e0a800;
  --warning-700: #b8860b;
  --warning-800: #996f00;
  --warning-900: #7a5a00;

  // Error/Danger colors
  --error: #dc3545;
  --error-50: #fdf2f2;
  --error-100: #fde8e8;
  --error-200: #fbd5d5;
  --error-300: #f8b4b4;
  --error-400: #f87171;
  --error-500: #dc3545;
  --error-600: #c82333;
  --error-700: #a02622;
  --error-800: #7f1d1d;
  --error-900: #651e1e;

  // Info colors
  --info: #17a2b8;
  --info-50: #ecfeff;
  --info-100: #cffafe;
  --info-200: #a5f3fc;
  --info-300: #67e8f9;
  --info-400: #22d3ee;
  --info-500: #17a2b8;
  --info-600: #138496;
  --info-700: #0e7490;
  --info-800: #155e75;
  --info-900: #164e63;

  // Neutral colors (Light theme)
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  --black: #000000;

  // Semantic colors for light theme
  --background: var(--white);
  --background-secondary: var(--gray-50);
  --background-tertiary: var(--gray-100);
  --surface: var(--white);
  --surface-secondary: var(--gray-50);

  --text: var(--gray-900);
  --text-secondary: var(--gray-600);
  --text-tertiary: var(--gray-500);
  --text-muted: var(--gray-400);

  --border: var(--gray-200);
  --border-secondary: var(--gray-300);
  --border-light: var(--gray-100);

  --shadow: rgba(0, 0, 0, 0.1);
  --shadow-light: rgba(0, 0, 0, 0.05);
  --shadow-dark: rgba(0, 0, 0, 0.15);

  // Component specific colors
  --navbar-bg: var(--white);
  --sidebar-bg: var(--white);
  --card-bg: var(--white);
  --modal-bg: var(--white);
  --input-bg: var(--white);
  --button-bg: var(--primary);
  --button-text: var(--white);

  // Hover states
  --primary-hover: var(--primary-600);
  --secondary-hover: var(--gray-300);
  --background-hover: var(--gray-50);
}

// Dark theme variables
[data-theme='dark'] {
  // Semantic colors for dark theme
  --background: var(--gray-900);
  --background-secondary: var(--gray-800);
  --background-tertiary: var(--gray-700);
  --surface: var(--gray-800);
  --surface-secondary: var(--gray-700);

  --text: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --text-muted: var(--gray-500);

  --border: var(--gray-700);
  --border-secondary: var(--gray-600);
  --border-light: var(--gray-800);

  --shadow: rgba(0, 0, 0, 0.3);
  --shadow-light: rgba(0, 0, 0, 0.2);
  --shadow-dark: rgba(0, 0, 0, 0.4);

  // Component specific colors for dark theme
  --navbar-bg: var(--gray-800);
  --sidebar-bg: var(--gray-800);
  --card-bg: var(--gray-800);
  --modal-bg: var(--gray-800);
  --input-bg: var(--gray-700);
  --button-bg: var(--primary);
  --button-text: var(--white);

  // Hover states for dark theme
  --primary-hover: var(--primary-400);
  --secondary-hover: var(--gray-600);
  --background-hover: var(--gray-700);
}

// Auto dark mode based on system preference
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    // Apply dark theme colors when system prefers dark and no theme is set
    --background: var(--gray-900);
    --background-secondary: var(--gray-800);
    --background-tertiary: var(--gray-700);
    --surface: var(--gray-800);
    --surface-secondary: var(--gray-700);

    --text: var(--gray-100);
    --text-secondary: var(--gray-300);
    --text-tertiary: var(--gray-400);
    --text-muted: var(--gray-500);

    --border: var(--gray-700);
    --border-secondary: var(--gray-600);
    --border-light: var(--gray-800);

    --shadow: rgba(0, 0, 0, 0.3);
    --shadow-light: rgba(0, 0, 0, 0.2);
    --shadow-dark: rgba(0, 0, 0, 0.4);

    --navbar-bg: var(--gray-800);
    --sidebar-bg: var(--gray-800);
    --card-bg: var(--gray-800);
    --modal-bg: var(--gray-800);
    --input-bg: var(--gray-700);
    --button-bg: var(--primary);
    --button-text: var(--white);

    --primary-hover: var(--primary-400);
    --secondary-hover: var(--gray-600);
    --background-hover: var(--gray-700);
  }
}
