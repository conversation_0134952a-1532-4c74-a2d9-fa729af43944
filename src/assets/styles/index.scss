// 样式系统入口文件
// 使用现代 @use 语法替代已废弃的 @import

// 主题文件 - 定义CSS变量
@use './theme.scss';

// 变量文件 - 定义SCSS变量
@use './variables.scss' as vars;

// 基础样式
@use './base.scss';

// 主样式文件
@use './main.scss';

/*
使用示例：
在其他 .scss 文件中使用变量：

@use './variables.scss' as vars;

.my-component {
  background-color: vars.$primary-color;
  color: vars.$white;
  border: 1px solid vars.$gray-200;
}

或者直接使用CSS变量：
.my-component {
  background-color: var(--primary, #02b980);
  color: var(--white, #ffffff);
}
*/
