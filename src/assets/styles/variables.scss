// Primary color palette based on #02b980
$primary-color: var(--primary, #02b980);
$primary-light: var(--primary-400, #3dd5a8);
$primary-lighter: var(--primary-300, #59e8af);
$primary-dark: var(--primary-600, #01a16f);
$primary-darker: var(--primary-700, #018a5e);

// Secondary colors
$secondary-color: var(--secondary, #6c757d);
$secondary-light: var(--secondary-400, #adb5bd);
$secondary-dark: var(--secondary-700, #495057);

// Success colors
$success-color: var(--success, #28a745);
$success-light: var(--success-400, #5cbf83);
$success-dark: var(--success-700, #1e7e34);

// Warning colors
$warning-color: var(--warning, #ffc107);
$warning-light: var(--warning-400, #ffcd39);
$warning-dark: var(--warning-600, #e0a800);

// Error colors
$error-color: var(--error, #dc3545);
$error-light: var(--error-400, #f87171);
$error-dark: var(--error-600, #c82333);

// Info colors
$info-color: var(--info, #17a2b8);
$info-light: var(--info-400, #22d3ee);
$info-dark: var(--info-600, #138496);

// Neutral colors
$white: var(--white, #ffffff);
$gray-50: var(--gray-50, #f9fafb);
$gray-100: var(--gray-100, #f3f4f6);
$gray-200: var(--gray-200, #e5e7eb);
$gray-300: var(--gray-300, #d1d5db);
$gray-400: var(--gray-400, #9ca3af);
$gray-500: var(--gray-500, #6b7280);
$gray-600: var(--gray-600, #4b5563);
$gray-700: var(--gray-700, #374151);
$gray-800: var(--gray-800, #1f2937);
$gray-900: var(--gray-900, #111827);
$black: var(--black, #000000);

// Typography
$font-family-base:
  'Inter',
  -apple-system,
  BlinkMacSystemFont,
  'Segoe UI',
  Roboto,
  Oxygen,
  Ubuntu,
  Cantarell,
  'Fira Sans',
  'Droid Sans',
  'Helvetica Neue',
  sans-serif;
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-2xl: 24px;
$font-size-3xl: 30px;
$font-size-4xl: 36px;

// Spacing
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-base: 12px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-2xl: 48px;
$spacing-3xl: 64px;

// Border radius
$border-radius-none: 0;
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-2xl: 24px;
$border-radius-full: 9999px;

// Border
$border-color: rgba(142, 152, 174, 0.14);

// Shadows
$shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-md:
  0 4px 6px -1px rgba(0, 0, 0, 0.1),
  0 2px 4px -1px rgba(0, 0, 0, 0.06);
$shadow-lg:
  0 10px 15px -3px rgba(0, 0, 0, 0.1),
  0 4px 6px -2px rgba(0, 0, 0, 0.05);
$shadow-xl:
  0 20px 25px -5px rgba(0, 0, 0, 0.1),
  0 10px 10px -5px rgba(0, 0, 0, 0.04);

// Z-index
$z-index-dropdown: 1000;
$z-index-sticky: 1020;
$z-index-fixed: 1030;
$z-index-modal-backdrop: 1040;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;

// Breakpoints
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-2xl: 1400px;

// Transition
$transition-base: all 0.3s ease;
$transition-fast: all 0.15s ease;
$transition-slow: all 0.5s ease;
