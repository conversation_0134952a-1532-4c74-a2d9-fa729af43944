<template>
  <a-layout>
    <a-layout-header class="header">
      <div class="header-left">
        <BaseBreadCrumb />
      </div>
      <div class="header-right">
        <a-space>
          <a-dropdown>
            <a-button type="text" size="small"> 李明华同学 </a-button>
            <template #overlay>
              <a-menu>
                <a-menu-item key="profile">个人资料</a-menu-item>
                <a-menu-item key="settings">设置</a-menu-item>
                <a-menu-divider />
                <a-menu-item key="logout">退出登录</a-menu-item>
              </a-menu>
            </template>
          </a-dropdown>
        </a-space>
      </div>
    </a-layout-header>
  </a-layout>
</template>

<script lang="ts" setup>
import BaseBreadCrumb from './BaseBreadCrumb.vue'
</script>

<style lang="scss" scoped>
/* 头部样式 */
.header {
  background: #fff;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid $border-color;

  .header-left {
    display: flex;
    align-items: center;
  }

  .header-right {
    display: flex;
    align-items: center;
  }
}
</style>
