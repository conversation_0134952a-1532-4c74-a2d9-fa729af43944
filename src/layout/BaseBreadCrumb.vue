<template>
  <a-breadcrumb class="breadcrumb">
    <a-breadcrumb-item v-for="(item, index) in breadcrumbList" :key="index">
      <router-link v-if="item.path && index < breadcrumbList.length - 1" :to="item.path">
        {{ item.title }}
      </router-link>
      <span v-else>{{ item.title }}</span>
    </a-breadcrumb-item>
  </a-breadcrumb>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import type { RouteLocationNormalizedLoaded } from 'vue-router'

interface BreadcrumbItem {
  title: string
  path?: string
  isParent?: boolean
}

const route = useRoute()
const router = useRouter()
const breadcrumbList = ref<BreadcrumbItem[]>([])

// 获取模块的第一个子路由路径
const getFirstChildPath = (parentPath: string) => {
  // 从路由器选项中获取原始路由配置
  const routerOptions = router.options.routes
  const parentRoute = routerOptions.find((r) => r.path === parentPath)

  if (parentRoute && parentRoute.children && parentRoute.children.length > 0) {
    // 获取第一个子路由
    const firstChild = parentRoute.children[0]
    // 构建完整路径
    return parentPath + '/' + firstChild.path
  }

  return parentPath
}

// 根据路由生成面包屑数据
const generateBreadcrumb = (route: RouteLocationNormalizedLoaded) => {
  const breadcrumbs: BreadcrumbItem[] = []

  // 如果是首页，只显示首页
  if (route.path === '/' || route.path === '/home') {
    breadcrumbs.push({
      title: '首页',
      path: '/',
      isParent: false,
    })
    return breadcrumbs
  }

  // 获取当前路由的匹配记录
  const matched = route.matched

  // 遍历所有匹配的路由
  for (let i = 0; i < matched.length; i++) {
    const item = matched[i]

    // 跳过根路径
    if (item.path === '/') {
      continue
    }

    // 如果路由有meta.title，则添加到面包屑
    if (item.meta && item.meta.title) {
      let itemPath = item.path

      // 如果是父级路由且有子路由，获取第一个子路由的路径
      if (item.children && item.children.length > 0) {
        itemPath = getFirstChildPath(item.path)
      }

      breadcrumbs.push({
        title: item.meta.title as string,
        path: itemPath,
        isParent: item.children && item.children.length > 0,
      })
    }
  }

  return breadcrumbs
}

// 监听路由变化，更新面包屑
watch(
  () => route.path,
  () => {
    breadcrumbList.value = generateBreadcrumb(route)
  },
  { immediate: true },
)
</script>
